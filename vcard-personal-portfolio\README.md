# <PERSON> - Personal Portfolio

A modern, fully responsive personal portfolio website showcasing my work as an AI Undergraduate and Full-Stack Developer. Built with HTML, CSS, and JavaScript, featuring a sleek dark theme with golden accents.

## ✨ Features

- **Responsive Design**: Optimized for all devices (desktop, tablet, mobile)
- **Modern UI/UX**: Clean, professional design with dark theme and golden accents
- **Interactive Elements**: Smooth animations and hover effects
- **Project Showcase**: Detailed project cards with technology tags
- **Resume Download**: Direct PDF download functionality
- **Contact Form**: Functional contact form for inquiries
- **Social Links**: Easy access to social media profiles

## 🛠️ Technologies Used

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Styling**: Custom CSS with CSS Grid and Flexbox
- **Icons**: Ionicons
- **Fonts**: Google Fonts (Poppins)
- **Responsive**: Mobile-first approach

## 📱 Demo

![Portfolio Desktop Demo](./website-demo-image/desktop.png "Desktop Demo")
![Portfolio Mobile Demo](./website-demo-image/mobile.png "Mobile Demo")

## 📁 Project Structure

```
vcard-personal-portfolio/
├── assets/
│   ├── css/
│   │   └── style.css          # Main stylesheet
│   ├── js/
│   │   └── script.js          # JavaScript functionality
│   ├── images/                # Project images and icons
│   └── files/
│       └── resume.pdf         # Downloadable resume
├── website-demo-image/        # Demo screenshots
├── index.html                 # Main HTML file
├── README.md                  # Project documentation
└── LICENSE                    # MIT License
```

## 🚀 Getting Started

1. **Clone the repository**
   ```bash
   git clone https://github.com/YOUR_USERNAME/YOUR_REPO_NAME.git
   ```

2. **Navigate to project directory**
   ```bash
   cd vcard-personal-portfolio
   ```

3. **Open in browser**
   - Simply open `index.html` in your preferred browser
   - Or use a local server for development

